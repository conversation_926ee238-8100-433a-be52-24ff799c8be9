/**
 * <PERSON> Intelligence Service
 * Core data service for rich narrative analysis and visualization
 */

interface CharacterEvolution {
  initial_state: string;
  major_turning_points: Array<{
    chapter: number;
    event: string;
    impact: string;
  }>;
  final_state: string;
  growth_trajectory: string;
}

interface RelationshipEvolution {
  initial_dynamic: string;
  key_developments: Array<{
    chapter: number;
    event: string;
    relationship_change: number;
    new_dynamic: string;
  }>;
  current_status: string;
}

interface Character {
  character_id: string;
  character_name: string;
  all_aliases: string[];
  overall_importance: number;
  character_archetype: string;
  first_appearance_chapter: number;
  last_appearance_chapter: number;
  total_chapters_present: number;
  chapter_by_chapter_summary: Array<{
    chapter_number: number;
    presence_level: string;
    key_actions: string[];
    emotional_state: string;
    character_goals: string[];
    development_notes: string;
  }>;
  character_evolution: CharacterEvolution;
  core_motivations: string[];
  primary_conflicts: string[];
  speaking_patterns: string;
  relationship_centrality: number;
}

interface Relationship {
  relationship_id: string;
  character_a_id: string;
  character_b_id: string;
  relationship_classification: string;
  relationship_summary: string;
  relationship_evolution: RelationshipEvolution;
  interaction_timeline: Array<{
    chapter: number;
    interaction_type: string;
    interaction_summary: string;
    emotional_intensity: number;
    plot_significance: number;
  }>;
  overall_strength: number;
  relationship_stability: string;
  mutual_influence: string;
  shared_history: string[];
  future_implications: string;
}

interface StoryData {
  book_metadata: {
    book_title: string;
    chapters_analyzed: number;
    chapter_range: string;
    overall_themes: string[];
    major_plot_arcs: string[];
    primary_settings: string[];
    narrative_progression: string;
  };
  consolidated_characters: Character[];
  consolidated_relationships: Relationship[];
  character_network_analysis: {
    most_connected_characters: Array<{
      character_id: string;
      connection_count: number;
      network_influence: number;
    }>;
    character_clusters: Array<{
      cluster_name: string;
      members: string[];
      cluster_type: string;
      binding_factor: string;
    }>;
    relationship_patterns: string[];
  };
  narrative_insights: {
    character_development_trends: string[];
    relationship_dynamics: string[];
    plot_driving_relationships: Array<{
      relationship_id: string;
      plot_impact: string;
    }>;
    character_agency_ranking: Array<{
      character_id: string;
      agency_score: number;
      influence_type: string;
    }>;
  };
}

class StoryIntelligenceService {
  private storyData: StoryData | null = null;
  private characterIndex: Map<string, Character> = new Map();
  private relationshipIndex: Map<string, Relationship> = new Map();
  private chapterIndex: Map<number, any> = new Map();
  private loaded = false;
  private currentDataset: string = 'HarryPotter';

  async initialize(dataset?: string): Promise<void> {
    // If no dataset specified, use current dataset
    const targetDataset = dataset || this.currentDataset;

    // If switching datasets or not loaded, reload
    if (this.currentDataset !== targetDataset || !this.loaded) {
      this.loaded = false;
      this.currentDataset = targetDataset;
      this.clearIndexes();
    } else if (this.loaded) {
      return; // Already loaded for this dataset
    }

    try {
      console.log(`🔄 Loading story intelligence data for ${targetDataset}...`);
      const dataPath = this.getDataPath(targetDataset);
      const response = await fetch(dataPath);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      this.storyData = await response.json();

      if (!this.storyData || !this.storyData.consolidated_characters || !this.storyData.consolidated_relationships) {
        throw new Error('Invalid story data structure');
      }

      this.buildIndexes();
      this.loaded = true;
      console.log(`📚 Story Intelligence Service initialized for ${targetDataset} with`,
        this.storyData.consolidated_characters.length, 'characters and',
        this.storyData.consolidated_relationships.length, 'relationships');
    } catch (error) {
      console.error(`❌ Failed to load story data for ${targetDataset}:`, error);
      throw error;
    }
  }

  private getDataPath(dataset: string): string {
    const dataPaths = {
      'HarryPotter': './hp_data/all_chapters.json',
      'Vampire': './vampire_data/outputs/c1_10_combined_book.json',
      'Vampire30': './vampire_data/outputs_combined/c_30_combined_book.json'
    };
    return dataPaths[dataset] || dataPaths['HarryPotter'];
  }

  // Success Pattern Analysis Methods
  getSuccessMetrics(): any {
    if (!this.loaded || !this.storyData) return null;

    const protagonist = this.getProtagonist();
    const relationships = this.storyData.consolidated_relationships || [];
    const insights = this.storyData.narrative_insights || {};

    return {
      powerProgression: this.analyzePowerProgression(protagonist),
      emotionalIntensity: this.analyzeEmotionalIntensity(relationships),
      characterArchetypes: this.analyzeCharacterArchetypes(),
      secretComplexity: this.analyzeSecretComplexity(),
      relationshipVolatility: this.analyzeRelationshipVolatility(relationships),
      conflictEscalation: this.analyzeConflictEscalation(),
      worldBuildingDepth: this.analyzeWorldBuilding()
    };
  }

  private getProtagonist(): Character | null {
    return this.storyData.consolidated_characters?.find(char =>
      char.character_archetype?.includes('protagonist') || char.overall_importance === 10
    ) || null;
  }

  private analyzePowerProgression(protagonist: Character | null): any {
    if (!protagonist?.chapter_by_chapter_summary) return null;

    const chapters = protagonist.chapter_by_chapter_summary;
    const turningPoints = protagonist.character_evolution?.major_turning_points || [];

    // Calculate progression velocity
    const progressionEvents = chapters.filter(ch =>
      ch.development_notes?.toLowerCase().includes('level') ||
      ch.development_notes?.toLowerCase().includes('power') ||
      ch.development_notes?.toLowerCase().includes('skill')
    );

    return {
      totalLevels: turningPoints.length,
      progressionVelocity: progressionEvents.length / chapters.length,
      majorTurningPoints: turningPoints.length,
      averageChaptersBetweenGrowth: chapters.length / Math.max(turningPoints.length, 1),
      growthTrajectory: protagonist.character_evolution?.growth_trajectory || 'unknown',
      finalState: protagonist.character_evolution?.final_state || '',
      successScore: this.calculateProgressionScore(progressionEvents.length, turningPoints.length, chapters.length)
    };
  }

  private calculateProgressionScore(events: number, turningPoints: number, totalChapters: number): number {
    // Optimal progression: 1 major growth every 5-7 chapters, steady minor progress
    const optimalTurningPoints = totalChapters / 6;
    const optimalEvents = totalChapters * 0.3;

    const turningPointScore = Math.min(turningPoints / optimalTurningPoints, 1) * 50;
    const eventScore = Math.min(events / optimalEvents, 1) * 50;

    return Math.round(turningPointScore + eventScore);
  }

  private analyzeEmotionalIntensity(relationships: any[]): any {
    const allInteractions = relationships.flatMap(rel => rel.interaction_timeline || []);
    const intensityPeaks = allInteractions.filter(interaction => interaction.emotional_intensity >= 8);
    const maxIntensity = Math.max(...allInteractions.map(i => i.emotional_intensity || 0));

    // Calculate peak frequency
    const chapterSpread = allInteractions.map(i => i.chapter).filter(Boolean);
    const minChapter = Math.min(...chapterSpread);
    const maxChapter = Math.max(...chapterSpread);
    const totalChapters = maxChapter - minChapter + 1;

    return {
      totalPeaks: intensityPeaks.length,
      maxIntensity,
      averageIntensity: allInteractions.reduce((sum, i) => sum + (i.emotional_intensity || 0), 0) / allInteractions.length,
      peakFrequency: intensityPeaks.length / totalChapters,
      optimalPacing: this.isOptimalEmotionalPacing(intensityPeaks, totalChapters),
      intensityTrend: this.calculateIntensityTrend(allInteractions),
      successScore: this.calculateEmotionalScore(intensityPeaks.length, maxIntensity, totalChapters)
    };
  }

  private isOptimalEmotionalPacing(peaks: any[], totalChapters: number): boolean {
    // Optimal: 1 major peak every 8-12 chapters
    const optimalFrequency = totalChapters / 10;
    return Math.abs(peaks.length - optimalFrequency) <= 2;
  }

  private calculateIntensityTrend(interactions: any[]): string {
    const sortedByChapter = interactions
      .filter(i => i.chapter && i.emotional_intensity)
      .sort((a, b) => a.chapter - b.chapter);

    if (sortedByChapter.length < 3) return 'insufficient_data';

    const firstHalf = sortedByChapter.slice(0, Math.floor(sortedByChapter.length / 2));
    const secondHalf = sortedByChapter.slice(Math.floor(sortedByChapter.length / 2));

    const firstAvg = firstHalf.reduce((sum, i) => sum + i.emotional_intensity, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, i) => sum + i.emotional_intensity, 0) / secondHalf.length;

    if (secondAvg > firstAvg + 1) return 'escalating';
    if (firstAvg > secondAvg + 1) return 'declining';
    return 'stable';
  }

  private calculateEmotionalScore(peaks: number, maxIntensity: number, totalChapters: number): number {
    const peakScore = Math.min(peaks / (totalChapters / 8), 1) * 40;
    const intensityScore = Math.min(maxIntensity / 10, 1) * 40;
    const consistencyScore = peaks > 0 ? 20 : 0;

    return Math.round(peakScore + intensityScore + consistencyScore);
  }

  private analyzeCharacterArchetypes(): any {
    const characters = this.storyData.consolidated_characters || [];
    const archetypes = characters.reduce((acc, char) => {
      const archetype = char.character_archetype || 'unknown';
      acc[archetype] = (acc[archetype] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const protagonist = characters.find(c => c.character_archetype?.includes('protagonist'));
    const agencyRanking = this.storyData.narrative_insights?.character_agency_ranking || [];

    return {
      archetypeDistribution: archetypes,
      totalCharacters: characters.length,
      protagonistAgency: protagonist ? agencyRanking.find(a => a.character_id === protagonist.character_id)?.agency_score || 0 : 0,
      supportingCharacters: characters.filter(c => c.character_archetype?.includes('supporting')).length,
      antagonists: characters.filter(c => c.character_archetype?.includes('antagonist')).length,
      balanceScore: this.calculateArchetypeBalance(archetypes, characters.length),
      successScore: this.calculateArchetypeScore(archetypes, protagonist, agencyRanking)
    };
  }

  private calculateArchetypeBalance(archetypes: Record<string, number>, total: number): number {
    // Optimal balance: 1 protagonist, 2-3 supporting, 1-2 antagonists, rest minor
    const protagonist = archetypes['protagonist'] || 0;
    const supporting = (archetypes['supporting'] || 0) + (archetypes['mentor|supporting'] || 0);
    const antagonist = archetypes['antagonist'] || 0;

    let score = 0;
    if (protagonist === 1) score += 30; // Perfect protagonist count
    if (supporting >= 2 && supporting <= 4) score += 30; // Good support system
    if (antagonist >= 1 && antagonist <= 3) score += 25; // Adequate conflict
    if (total >= 5 && total <= 15) score += 15; // Manageable cast size

    return score;
  }

  private calculateArchetypeScore(archetypes: Record<string, number>, protagonist: any, agencyRanking: any[]): number {
    let score = 0;

    // Protagonist dominance
    if (protagonist && agencyRanking.find(a => a.character_id === protagonist.character_id)?.agency_score >= 9) {
      score += 40;
    }

    // Character variety
    const uniqueArchetypes = Object.keys(archetypes).length;
    score += Math.min(uniqueArchetypes * 5, 30);

    // Balance
    score += this.calculateArchetypeBalance(archetypes, Object.values(archetypes).reduce((a, b) => a + b, 0)) * 0.3;

    return Math.round(score);
  }

  private analyzeSecretComplexity(): any {
    const relationships = this.storyData.consolidated_relationships || [];
    const characters = this.storyData.consolidated_characters || [];

    // Count secret-based relationships
    const secretRelationships = relationships.filter(rel =>
      rel.relationship_summary?.toLowerCase().includes('secret') ||
      rel.relationship_summary?.toLowerCase().includes('hidden') ||
      rel.relationship_summary?.toLowerCase().includes('mystery')
    );

    // Count characters with secrets
    const secretKeepers = characters.filter(char =>
      char.core_motivations?.some(m => m.toLowerCase().includes('secret')) ||
      char.primary_conflicts?.some(c => c.toLowerCase().includes('secret')) ||
      char.character_evolution?.major_turning_points?.some(tp => tp.event.toLowerCase().includes('secret'))
    );

    // Analyze revelation timing
    const revelations = relationships.flatMap(rel =>
      rel.relationship_evolution?.key_developments?.filter(dev =>
        dev.event.toLowerCase().includes('reveal') ||
        dev.event.toLowerCase().includes('discover')
      ) || []
    );

    return {
      secretRelationships: secretRelationships.length,
      secretKeepers: secretKeepers.length,
      totalRevelations: revelations.length,
      averageRevelationChapter: revelations.length > 0 ?
        revelations.reduce((sum, r) => sum + r.chapter, 0) / revelations.length : 0,
      secretLayers: this.calculateSecretLayers(),
      informationAsymmetry: this.calculateInformationAsymmetry(relationships),
      successScore: this.calculateSecretScore(secretRelationships.length, secretKeepers.length, revelations.length)
    };
  }

  private calculateSecretLayers(): number {
    // Count different types of secrets in the story
    const protagonist = this.getProtagonist();
    if (!protagonist) return 0;

    let layers = 0;
    const summary = protagonist.character_evolution?.final_state || '';
    const conflicts = protagonist.primary_conflicts || [];

    if (summary.includes('secret') || conflicts.some(c => c.includes('secret'))) layers++;
    if (summary.includes('power') || summary.includes('ability')) layers++;
    if (summary.includes('identity') || summary.includes('nature')) layers++;

    return layers;
  }

  private calculateInformationAsymmetry(relationships: any[]): number {
    // Count relationships where one character knows something the other doesn't
    return relationships.filter(rel =>
      rel.relationship_summary?.includes('unaware') ||
      rel.relationship_summary?.includes('hidden') ||
      rel.relationship_summary?.includes('secret')
    ).length;
  }

  private calculateSecretScore(secretRels: number, secretKeepers: number, revelations: number): number {
    const relScore = Math.min(secretRels * 15, 40);
    const keeperScore = Math.min(secretKeepers * 10, 30);
    const revelationScore = Math.min(revelations * 5, 30);

    return Math.round(relScore + keeperScore + revelationScore);
  }

  private analyzeRelationshipVolatility(relationships: any[]): any {
    const volatileRelationships = relationships.filter(rel =>
      rel.relationship_stability === 'volatile' ||
      rel.relationship_stability === 'unstable'
    );

    const evolutionData = relationships.map(rel => {
      const developments = rel.relationship_evolution?.key_developments || [];
      const changes = developments.map(d => Math.abs(d.relationship_change || 0));
      return {
        id: rel.relationship_id,
        totalChanges: changes.reduce((sum, c) => sum + c, 0),
        changeFrequency: changes.length,
        maxChange: Math.max(...changes, 0),
        stability: rel.relationship_stability
      };
    });

    const averageVolatility = evolutionData.reduce((sum, rel) => sum + rel.totalChanges, 0) / evolutionData.length;

    return {
      volatileCount: volatileRelationships.length,
      totalRelationships: relationships.length,
      volatilityRatio: volatileRelationships.length / relationships.length,
      averageVolatility,
      highestVolatility: Math.max(...evolutionData.map(r => r.totalChanges)),
      relationshipEvolution: evolutionData,
      dramaScore: this.calculateDramaScore(volatileRelationships.length, averageVolatility),
      successScore: this.calculateVolatilityScore(volatileRelationships.length, relationships.length, averageVolatility)
    };
  }

  private calculateDramaScore(volatileCount: number, avgVolatility: number): number {
    // Drama comes from relationship instability
    const volatileScore = Math.min(volatileCount * 20, 60);
    const intensityScore = Math.min(avgVolatility * 5, 40);
    return Math.round(volatileScore + intensityScore);
  }

  private calculateVolatilityScore(volatileCount: number, totalRels: number, avgVolatility: number): number {
    // Optimal: 30-50% of relationships should be volatile for drama
    const optimalRatio = 0.4;
    const ratioScore = Math.max(0, 50 - Math.abs((volatileCount / totalRels) - optimalRatio) * 100);
    const intensityScore = Math.min(avgVolatility * 10, 50);

    return Math.round(ratioScore + intensityScore);
  }

  private analyzeConflictEscalation(): any {
    const relationships = this.storyData.consolidated_relationships || [];
    const conflictRelationships = relationships.filter(rel =>
      rel.relationship_classification === 'conflict' ||
      rel.relationship_classification === 'complex'
    );

    const conflictTypes = relationships.flatMap(rel =>
      rel.interaction_timeline?.map(interaction => interaction.interaction_type) || []
    );

    const conflictTypeCount = conflictTypes.reduce((acc, type) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Analyze escalation pattern
    const escalationPattern = this.analyzeEscalationPattern(relationships);

    return {
      conflictRelationships: conflictRelationships.length,
      conflictTypes: conflictTypeCount,
      escalationPattern,
      conflictDiversity: Object.keys(conflictTypeCount).length,
      conflictIntensity: this.calculateConflictIntensity(relationships),
      successScore: this.calculateConflictScore(conflictRelationships.length, Object.keys(conflictTypeCount).length)
    };
  }

  private analyzeEscalationPattern(relationships: any[]): string {
    const allInteractions = relationships.flatMap(rel => rel.interaction_timeline || [])
      .filter(i => i.chapter && i.interaction_type)
      .sort((a, b) => a.chapter - b.chapter);

    if (allInteractions.length < 3) return 'insufficient_data';

    const conflictTypes = ['conflict', 'confrontation', 'fight', 'battle'];
    const conflictInteractions = allInteractions.filter(i =>
      conflictTypes.some(type => i.interaction_type.includes(type))
    );

    if (conflictInteractions.length < 2) return 'low_conflict';

    // Check if conflicts get more intense over time
    const firstHalf = conflictInteractions.slice(0, Math.floor(conflictInteractions.length / 2));
    const secondHalf = conflictInteractions.slice(Math.floor(conflictInteractions.length / 2));

    const firstAvgIntensity = firstHalf.reduce((sum, i) => sum + (i.emotional_intensity || 0), 0) / firstHalf.length;
    const secondAvgIntensity = secondHalf.reduce((sum, i) => sum + (i.emotional_intensity || 0), 0) / secondHalf.length;

    if (secondAvgIntensity > firstAvgIntensity + 1) return 'escalating';
    if (firstAvgIntensity > secondAvgIntensity + 1) return 'de-escalating';
    return 'stable';
  }

  private calculateConflictIntensity(relationships: any[]): number {
    const conflictInteractions = relationships.flatMap(rel => rel.interaction_timeline || [])
      .filter(i => i.interaction_type === 'conflict');

    if (conflictInteractions.length === 0) return 0;

    return conflictInteractions.reduce((sum, i) => sum + (i.emotional_intensity || 0), 0) / conflictInteractions.length;
  }

  private calculateConflictScore(conflictRels: number, conflictTypes: number): number {
    const relScore = Math.min(conflictRels * 15, 50);
    const diversityScore = Math.min(conflictTypes * 10, 50);

    return Math.round(relScore + diversityScore);
  }

  private analyzeWorldBuilding(): any {
    const metadata = this.storyData.book_metadata || {};
    const settings = (metadata as any).primary_settings || [];
    const themes = (metadata as any).overall_themes || [];
    const plotArcs = (metadata as any).major_plot_arcs || [];
    const narrativeProgression = (metadata as any).narrative_progression || '';

    // Analyze setting types and usage
    const settingAnalysis = this.analyzeSettings(settings);
    const themeAnalysis = this.analyzeThemes(themes);
    const plotArcAnalysis = this.analyzePlotArcs(plotArcs);

    return {
      settingCount: settings.length,
      settings: settings,
      settingAnalysis,
      themeCount: themes.length,
      themes: themes,
      themeAnalysis,
      plotArcCount: plotArcs.length,
      plotArcs: plotArcs,
      plotArcAnalysis,
      narrativeProgression,
      settingDiversity: this.calculateSettingDiversity(settings),
      thematicDepth: this.calculateThematicDepth(themes),
      narrativeComplexity: plotArcs.length,
      worldBuildingScore: this.calculateWorldBuildingScore(settings.length, themes.length, plotArcs.length),
      immersionFactors: this.calculateImmersionFactors(settings, themes, plotArcs),
      franchisePotential: this.calculateFranchisePotential(settings, themes, plotArcs),
      successScore: this.calculateWorldBuildingScore(settings.length, themes.length, plotArcs.length)
    };
  }

  private analyzeSettings(settings: string[]): any {
    const settingTypes = {
      'Educational': ['school', 'academy', 'library', 'classroom'],
      'Residential': ['dorm', 'room', 'home', 'house'],
      'Social': ['canteen', 'hall', 'assembly'],
      'Training': ['training', 'weapons', 'combat', 'gym'],
      'Medical': ['doctor', 'medical', 'hospital', 'clinic'],
      'Outdoor': ['wasteland', 'testing', 'field', 'outdoor'],
      'Administrative': ['office', 'administration', 'headquarters']
    };

    const categorizedSettings = {};
    const settingDetails = settings.map(setting => {
      const category = Object.keys(settingTypes).find(type =>
        settingTypes[type].some(keyword => setting.toLowerCase().includes(keyword))
      ) || 'Other';

      if (!categorizedSettings[category]) categorizedSettings[category] = [];
      categorizedSettings[category].push(setting);

      return {
        name: setting,
        category,
        atmosphereScore: this.calculateSettingAtmosphere(setting),
        conflictPotential: this.calculateSettingConflictPotential(setting)
      };
    });

    return {
      categorizedSettings,
      settingDetails,
      dominantCategory: Object.keys(categorizedSettings).reduce((a, b) =>
        categorizedSettings[a].length > categorizedSettings[b].length ? a : b
      ),
      atmosphereVariety: settingDetails.map(s => s.atmosphereScore).reduce((sum, score) => sum + score, 0) / settingDetails.length
    };
  }

  private calculateSettingAtmosphere(setting: string): number {
    const atmosphereKeywords = {
      'tense': ['training', 'weapons', 'combat', 'testing'],
      'social': ['canteen', 'hall', 'assembly'],
      'intimate': ['dorm', 'room', 'office'],
      'academic': ['library', 'academy', 'school'],
      'dangerous': ['wasteland', 'testing', 'combat']
    };

    let score = 5; // base score
    Object.keys(atmosphereKeywords).forEach(mood => {
      if (atmosphereKeywords[mood].some(keyword => setting.toLowerCase().includes(keyword))) {
        score += 2;
      }
    });

    return Math.min(score, 10);
  }

  private calculateSettingConflictPotential(setting: string): number {
    const conflictKeywords = ['training', 'weapons', 'combat', 'testing', 'wasteland', 'assembly'];
    const socialKeywords = ['canteen', 'hall', 'dorm'];

    let potential = 3; // base potential
    if (conflictKeywords.some(keyword => setting.toLowerCase().includes(keyword))) {
      potential += 4;
    }
    if (socialKeywords.some(keyword => setting.toLowerCase().includes(keyword))) {
      potential += 2;
    }

    return Math.min(potential, 10);
  }

  private analyzeThemes(themes: string[]): any {
    const themeCategories = {
      'Power Dynamics': ['power', 'hierarchy', 'dominance', 'control'],
      'Social Issues': ['bullying', 'oppression', 'social', 'class'],
      'Personal Growth': ['transformation', 'identity', 'progression', 'development'],
      'Relationships': ['friendship', 'loyalty', 'betrayal', 'love'],
      'Moral Conflict': ['moral', 'ethics', 'right', 'wrong', 'conflict'],
      'Mystery': ['secrecy', 'hidden', 'mystery', 'unknown']
    };

    const categorizedThemes = {};
    const themeDetails = themes.map(theme => {
      const category = Object.keys(themeCategories).find(cat =>
        themeCategories[cat].some(keyword => theme.toLowerCase().includes(keyword))
      ) || 'Other';

      if (!categorizedThemes[category]) categorizedThemes[category] = [];
      categorizedThemes[category].push(theme);

      return {
        name: theme,
        category,
        complexity: this.calculateThemeComplexity(theme),
        marketAppeal: this.calculateThemeMarketAppeal(theme)
      };
    });

    return {
      categorizedThemes,
      themeDetails,
      dominantCategory: Object.keys(categorizedThemes).reduce((a, b) =>
        categorizedThemes[a].length > categorizedThemes[b].length ? a : b
      ),
      averageComplexity: themeDetails.reduce((sum, t) => sum + t.complexity, 0) / themeDetails.length,
      averageMarketAppeal: themeDetails.reduce((sum, t) => sum + t.marketAppeal, 0) / themeDetails.length
    };
  }

  private calculateThemeComplexity(theme: string): number {
    const complexityIndicators = ['and', 'vs', 'between', 'conflict', 'dynamics'];
    let complexity = theme.length > 15 ? 6 : 4; // base complexity

    complexityIndicators.forEach(indicator => {
      if (theme.toLowerCase().includes(indicator)) {
        complexity += 2;
      }
    });

    return Math.min(complexity, 10);
  }

  private calculateThemeMarketAppeal(theme: string): number {
    const popularThemes = ['power', 'transformation', 'friendship', 'bullying', 'identity', 'secret'];
    let appeal = 5; // base appeal

    popularThemes.forEach(popular => {
      if (theme.toLowerCase().includes(popular)) {
        appeal += 1.5;
      }
    });

    return Math.min(appeal, 10);
  }

  private analyzePlotArcs(plotArcs: string[]): any {
    return plotArcs.map((arc, index) => ({
      id: index,
      description: arc,
      complexity: this.calculatePlotArcComplexity(arc),
      tension: this.calculatePlotArcTension(arc),
      characterFocus: this.extractCharacterFocus(arc),
      arcType: this.classifyPlotArc(arc)
    }));
  }

  private calculatePlotArcComplexity(arc: string): number {
    const complexityWords = ['alliance', 'confrontation', 'development', 'transformation', 'mystery'];
    let complexity = 3;

    complexityWords.forEach(word => {
      if (arc.toLowerCase().includes(word)) complexity += 1.5;
    });

    return Math.min(complexity, 10);
  }

  private calculatePlotArcTension(arc: string): number {
    const tensionWords = ['confrontation', 'conflict', 'battle', 'struggle', 'danger', 'threat'];
    let tension = 4;

    tensionWords.forEach(word => {
      if (arc.toLowerCase().includes(word)) tension += 2;
    });

    return Math.min(tension, 10);
  }

  private extractCharacterFocus(arc: string): string[] {
    const characters = ['Quinn', 'Vorden', 'Peter', 'Layla', 'Rylee', 'Leo'];
    return characters.filter(char => arc.includes(char));
  }

  private classifyPlotArc(arc: string): string {
    if (arc.toLowerCase().includes('transformation') || arc.toLowerCase().includes('discovery')) return 'Character Development';
    if (arc.toLowerCase().includes('alliance') || arc.toLowerCase().includes('friendship')) return 'Relationship Building';
    if (arc.toLowerCase().includes('confrontation') || arc.toLowerCase().includes('conflict')) return 'Conflict Resolution';
    if (arc.toLowerCase().includes('secret') || arc.toLowerCase().includes('mystery')) return 'Mystery/Revelation';
    return 'General Progression';
  }

  private calculateImmersionFactors(settings: string[], themes: string[], plotArcs: string[]): any {
    return {
      environmentalRichness: settings.length * 12.5, // Max 100 for 8 settings
      thematicDepth: themes.length * 12.5, // Max 100 for 8 themes
      narrativeComplexity: plotArcs.length * 16.67, // Max 100 for 6 arcs
      overallImmersion: ((settings.length * 12.5) + (themes.length * 12.5) + (plotArcs.length * 16.67)) / 3
    };
  }

  private calculateFranchisePotential(settings: string[], themes: string[], plotArcs: string[]): any {
    const worldExpandability = settings.length >= 6 ? 25 : settings.length * 4;
    const thematicVersatility = themes.length >= 6 ? 25 : themes.length * 4;
    const narrativeScalability = plotArcs.length >= 4 ? 25 : plotArcs.length * 6;
    const characterDevelopmentPotential = 25; // Based on existing character depth

    const totalScore = worldExpandability + thematicVersatility + narrativeScalability + characterDevelopmentPotential;

    return {
      worldExpandability,
      thematicVersatility,
      narrativeScalability,
      characterDevelopmentPotential,
      totalScore,
      franchiseViability: totalScore >= 80 ? 'High' : totalScore >= 60 ? 'Medium' : 'Low'
    };
  }

  private calculateSettingDiversity(settings: string[]): number {
    // More diverse settings = richer world
    const uniqueTypes = new Set(settings.map(setting =>
      setting.toLowerCase().split(' ')[0] // Get first word as type
    )).size;

    return Math.min(uniqueTypes / settings.length, 1) * 100;
  }

  private calculateThematicDepth(themes: string[]): number {
    // Complex themes indicate deeper storytelling
    const complexThemes = themes.filter(theme =>
      theme.includes('and') || theme.includes('vs') || theme.length > 20
    );

    return (complexThemes.length / themes.length) * 100;
  }

  private calculateWorldBuildingScore(settings: number, themes: number, plotArcs: number): number {
    const settingScore = Math.min(settings * 8, 40);
    const themeScore = Math.min(themes * 5, 30);
    const plotScore = Math.min(plotArcs * 5, 30);

    return Math.round(settingScore + themeScore + plotScore);
  }

  private clearIndexes(): void {
    this.characterIndex.clear();
    this.relationshipIndex.clear();
    this.chapterIndex.clear();
    this.storyData = null;
  }

  getCurrentDataset(): string {
    return this.currentDataset;
  }

  setCurrentDataset(dataset: string): void {
    this.currentDataset = dataset;
  }

  private buildIndexes(): void {
    if (!this.storyData) return;

    // Build character index
    this.storyData.consolidated_characters.forEach(char => {
      this.characterIndex.set(char.character_id, char);
    });

    // Build relationship index
    this.storyData.consolidated_relationships.forEach(rel => {
      this.relationshipIndex.set(rel.relationship_id, rel);
    });

    // Build chapter index for quick lookups
    this.storyData.consolidated_characters.forEach(char => {
      char.chapter_by_chapter_summary.forEach(chapterSummary => {
        const chapterNum = chapterSummary.chapter_number;
        if (!this.chapterIndex.has(chapterNum)) {
          this.chapterIndex.set(chapterNum, {
            characters: [],
            relationships: []
          });
        }
        this.chapterIndex.get(chapterNum)!.characters.push({
          character_id: char.character_id,
          ...chapterSummary
        });
      });
    });
  }

  // Core data access methods
  getCharacter(characterId: string): Character | undefined {
    return this.characterIndex.get(characterId);
  }

  getRelationship(relationshipId: string): Relationship | undefined {
    return this.relationshipIndex.get(relationshipId);
  }

  getAllCharacters(): Character[] {
    return this.storyData?.consolidated_characters || [];
  }

  getAllRelationships(): Relationship[] {
    return this.storyData?.consolidated_relationships || [];
  }

  getBookMetadata() {
    return this.storyData?.book_metadata;
  }

  getNetworkAnalysis() {
    return this.storyData?.character_network_analysis;
  }

  getNarrativeInsights() {
    return this.storyData?.narrative_insights;
  }

  // Character evolution methods
  getCharacterEvolution(characterId: string) {
    const character = this.getCharacter(characterId);
    return character?.character_evolution;
  }

  getCharacterTurningPoints(characterId: string) {
    const evolution = this.getCharacterEvolution(characterId);
    return evolution?.major_turning_points || [];
  }

  getCharactersByChapter(chapterNumber: number) {
    return this.chapterIndex.get(chapterNumber)?.characters || [];
  }

  // Relationship dynamics methods
  getRelationshipEvolution(relationshipId: string) {
    const relationship = this.getRelationship(relationshipId);
    return relationship?.relationship_evolution;
  }

  getRelationshipsByCharacter(characterId: string): Relationship[] {
    return this.getAllRelationships().filter(rel => 
      rel.character_a_id === characterId || rel.character_b_id === characterId
    );
  }

  getStrongestRelationships(limit: number = 10): Relationship[] {
    return this.getAllRelationships()
      .sort((a, b) => b.overall_strength - a.overall_strength)
      .slice(0, limit);
  }

  // Network analysis methods
  getMostInfluentialCharacters(limit: number = 10) {
    if (!this.storyData?.character_network_analysis?.most_connected_characters) return [];

    return this.storyData.character_network_analysis.most_connected_characters
      .slice(0, limit);
  }

  getMostInfluentialCharacterObjects(limit: number = 10): Character[] {
    if (!this.storyData?.character_network_analysis?.most_connected_characters) return [];

    return this.storyData.character_network_analysis.most_connected_characters
      .slice(0, limit)
      .map(item => this.characterIndex.get(item.character_id))
      .filter(char => char !== undefined) as Character[];
  }

  getCharacterClusters() {
    return this.storyData?.character_network_analysis?.character_clusters || [];
  }

  getRelationshipPatterns() {
    return this.storyData?.character_network_analysis?.relationship_patterns || [];
  }

  // Theme and narrative methods
  getOverallThemes() {
    return this.storyData?.book_metadata.overall_themes || [];
  }

  getMajorPlotArcs() {
    return this.storyData?.book_metadata.major_plot_arcs || [];
  }

  getCharacterAgencyRanking() {
    return this.storyData?.narrative_insights?.character_agency_ranking || [];
  }

  getThematicAnalysis() {
    if (!this.storyData) return null;
    return {
      themes: this.storyData.book_metadata?.overall_themes || [],
      plotArcs: this.storyData.book_metadata?.major_plot_arcs || [],
      characterDevelopment: this.storyData.narrative_insights?.character_development_trends || [],
      relationshipDynamics: this.storyData.narrative_insights?.relationship_dynamics || []
    };
  }

  // Search and filter methods
  searchCharacters(query: string): Character[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllCharacters().filter(char => 
      char.character_name.toLowerCase().includes(lowerQuery) ||
      char.all_aliases.some(alias => alias.toLowerCase().includes(lowerQuery))
    );
  }

  getCharactersByArchetype(archetype: string): Character[] {
    return this.getAllCharacters().filter(char => 
      char.character_archetype.includes(archetype)
    );
  }

  getRelationshipsByType(type: string): Relationship[] {
    return this.getAllRelationships().filter(rel => 
      rel.relationship_classification === type
    );
  }
}

// Create singleton instance
export const storyIntelligence = new StoryIntelligenceService();
export type { Character, Relationship, StoryData, CharacterEvolution, RelationshipEvolution };
